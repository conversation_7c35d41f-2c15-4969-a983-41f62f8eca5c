(function(global) {
    // Configuration and state
    let isModalInitialized = false;
    let isClosing = false;
    let currentChartInstance = null;
    let currentDiscData = null;

    async function showDiscProfile(discData = null) {
        try {
            if (discData) {
                currentDiscData = discData;
            }

            if (isModalInitialized) {
                await resetAndShowModal();
                return;
            }

            isClosing = false;

            // Create modal structure
            const overlay = document.createElement('div');
            overlay.id = 'disc-profile-overlay';
            overlay.className = 'modal-overlay';

            if (currentDiscData) {
                overlay.innerHTML = createDiscModalHTML(currentDiscData);
            } else {
                throw new Error('Invalid DISC data structure for modal creation');
            }

            document.body.appendChild(overlay);
            isModalInitialized = true;

            // Initialize event listeners
            initializeDiscEventListeners(overlay);

            // Create DISC chart with actual data
            await createDiscChart(currentDiscData);

            // Animate modal appearance
            requestAnimationFrame(() => {
                if (!isClosing) {
                    overlay.style.opacity = '1';
                    const modalContent = overlay.querySelector('.modal-content');
                    if (modalContent) {
                        modalContent.style.opacity = '1';
                        modalContent.style.transform = 'scale(1)';
                    }
                }
            });

        } catch (error) {
            console.error('Error showing DISC profile modal:', error);
            alert('Error displaying DISC profile. Please try again.');
        }
    }

    function createDiscModalHTML(discData) {
        const { scores, analysis, primaryStyle, secondaryStyle } = discData;

        return `
            <div class="modal-content disc-profile-modal">
                <div class="modal-header">
                    <div class="header-content">
                        <h2>Your DISC Personality Profile</h2>
                        <p class="modal-subtitle">Understanding your behavioral preferences and workplace style</p>
                    </div>
                    <button class="close-button" aria-label="Close modal">×</button>
                </div>

                <div class="modal-body">
                    <div class="disc-overview">
                        <div class="primary-styles">
                            <div class="style-badge primary">
                                <span class="style-letter">${primaryStyle || 'D'}</span>
                                <span class="style-label">Primary Style</span>
                            </div>
                            ${secondaryStyle ? `
                            <div class="style-badge secondary">
                                <span class="style-letter">${secondaryStyle}</span>
                                <span class="style-label">Secondary Style</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="disc-content-container">
                        <div class="disc-chart-section">
                            <h3>DISC Dimensions</h3>
                            <div class="chart-container">
                                <canvas id="disc-chart" width="300" height="300"></canvas>
                            </div>
                            <div class="disc-legend">
                                <div class="legend-item">
                                    <span class="legend-color d-color"></span>
                                    <span class="legend-label">Dominance (${scores.dominance || 0}%)</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color i-color"></span>
                                    <span class="legend-label">Influence (${scores.influence || 0}%)</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color s-color"></span>
                                    <span class="legend-label">Steadiness (${scores.steadiness || 0}%)</span>
                                </div>
                                <div class="legend-item">
                                    <span class="legend-color c-color"></span>
                                    <span class="legend-label">Compliance (${scores.compliance || 0}%)</span>
                                </div>
                            </div>
                        </div>

                        <div class="disc-analysis-section">
                            <h3>Your Behavioral Profile</h3>
                            <div class="analysis-content">
                                ${formatAnalysisContent(analysis)}
                            </div>
                        </div>
                    </div>

                    <div class="disc-dimensions-details">
                        <h3>Understanding the DISC Dimensions</h3>
                        <div class="dimensions-grid">
                            <div class="dimension-card d-dimension">
                                <h4><span class="dimension-letter">D</span>ominance</h4>
                                <p>How you approach problems and challenges</p>
                                <ul>
                                    <li>Direct and decisive</li>
                                    <li>Results-oriented</li>
                                    <li>Competitive</li>
                                    <li>Strong-willed</li>
                                </ul>
                            </div>
                            <div class="dimension-card i-dimension">
                                <h4><span class="dimension-letter">I</span>nfluence</h4>
                                <p>How you interact with and influence others</p>
                                <ul>
                                    <li>Outgoing and enthusiastic</li>
                                    <li>People-focused</li>
                                    <li>Optimistic</li>
                                    <li>Persuasive</li>
                                </ul>
                            </div>
                            <div class="dimension-card s-dimension">
                                <h4><span class="dimension-letter">S</span>teadiness</h4>
                                <p>How you respond to pace and consistency</p>
                                <ul>
                                    <li>Patient and supportive</li>
                                    <li>Team-oriented</li>
                                    <li>Reliable</li>
                                    <li>Stable</li>
                                </ul>
                            </div>
                            <div class="dimension-card c-dimension">
                                <h4><span class="dimension-letter">C</span>ompliance</h4>
                                <p>How you respond to rules and procedures</p>
                                <ul>
                                    <li>Analytical and precise</li>
                                    <li>Quality-focused</li>
                                    <li>Systematic</li>
                                    <li>Detail-oriented</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn-print" onclick="window.print()">
                        <i class="fas fa-print"></i> Print Profile
                    </button>
                    <button class="btn-close-disc">Close</button>
                </div>
            </div>
        `;
    }

    function formatAnalysisContent(analysis) {
        if (!analysis) return '<p>Analysis not available.</p>';

        // Handle analysis object structure
        if (typeof analysis === 'object') {
            let content = '';
            if (analysis.behavioralDescription) {
                content += `<div class="analysis-section">
                    <h4>Behavioral Description</h4>
                    <p>${analysis.behavioralDescription}</p>
                </div>`;
            }
            if (analysis.workplaceImplications) {
                content += `<div class="analysis-section">
                    <h4>Workplace Implications</h4>
                    <p>${analysis.workplaceImplications}</p>
                </div>`;
            }
            if (analysis.recommendations) {
                content += `<div class="analysis-section">
                    <h4>Professional Development</h4>
                    <p>${analysis.recommendations}</p>
                </div>`;
            }
            if (analysis.strengthsAndChallenges) {
                content += `<div class="analysis-section">
                    <h4>Strengths & Growth Areas</h4>
                    <p>${analysis.strengthsAndChallenges}</p>
                </div>`;
            }
            return content || '<p>Analysis not available.</p>';
        }

        // Handle string analysis (fallback)
        const paragraphs = analysis.split('\n').filter(p => p.trim().length > 0);
        return paragraphs.map(p => `<p>${p.trim()}</p>`).join('');
    }

    function initializeDiscEventListeners(overlay) {
        // Close button handlers
        const closeButtons = overlay.querySelectorAll('.close-button, .btn-close-disc');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => closeDiscModal());
        });

        // Close on overlay click (but not on modal content click)
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeDiscModal();
            }
        });

        // Escape key handler
        document.addEventListener('keydown', handleEscapeKey);
    }

    function handleEscapeKey(e) {
        if (e.key === 'Escape' && isModalInitialized && !isClosing) {
            closeDiscModal();
        }
    }

    async function createDiscChart(discData) {
        const canvas = document.getElementById('disc-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // Destroy existing chart if it exists
        if (currentChartInstance) {
            currentChartInstance.destroy();
        }

        const data = {
            labels: ['Dominance', 'Influence', 'Steadiness', 'Compliance'],
            datasets: [{
                label: 'DISC Profile',
                data: [
                    discData.scores.dominance || 0,
                    discData.scores.influence || 0,
                    discData.scores.steadiness || 0,
                    discData.scores.compliance || 0
                ],
                backgroundColor: [
                    'rgba(220, 53, 69, 0.8)',   // Red for Dominance
                    'rgba(255, 193, 7, 0.8)',   // Yellow for Influence
                    'rgba(40, 167, 69, 0.8)',   // Green for Steadiness
                    'rgba(0, 123, 255, 0.8)'    // Blue for Compliance
                ],
                borderColor: [
                    'rgba(220, 53, 69, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(0, 123, 255, 1)'
                ],
                borderWidth: 2
            }]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    display: false // We'll show our custom legend
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed}%`;
                        }
                    }
                }
            },
            scales: {
                r: {
                    angleLines: {
                        display: true
                    },
                    suggestedMin: 0,
                    suggestedMax: 100,
                    ticks: {
                        stepSize: 20,
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        };

        currentChartInstance = new Chart(ctx, {
            type: 'radar',
            data: data,
            options: options
        });
    }

    function closeDiscModal() {
        if (isClosing) return;
        isClosing = true;

        const overlay = document.getElementById('disc-profile-overlay');
        if (overlay) {
            const modalContent = overlay.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'scale(0.9)';
            }
            overlay.style.opacity = '0';

            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
                isModalInitialized = false;
                isClosing = false;
                
                // Destroy chart
                if (currentChartInstance) {
                    currentChartInstance.destroy();
                    currentChartInstance = null;
                }
                
                // Remove escape key listener
                document.removeEventListener('keydown', handleEscapeKey);
            }, 300);
        }
    }

    async function resetAndShowModal() {
        if (currentDiscData) {
            // Update content without recreating the entire modal
            const modalContent = document.querySelector('.disc-profile-modal');
            if (modalContent) {
                modalContent.innerHTML = createDiscModalHTML(currentDiscData).match(/<div class="modal-content[^>]*>(.*)<\/div>$/s)[1];
                await createDiscChart(currentDiscData);
            }
        }
    }

    // Add CSS styles for DISC modal
    function injectDiscModalStyles() {
        if (document.getElementById('disc-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'disc-modal-styles';
        style.textContent = `
            .disc-profile-modal {
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
            }

            .primary-styles {
                display: flex;
                justify-content: center;
                gap: 20px;
                margin: 20px 0;
            }

            .style-badge {
                text-align: center;
                padding: 15px;
                border-radius: 10px;
                border: 2px solid #ddd;
            }

            .style-badge.primary {
                border-color: #007bff;
                background-color: rgba(0, 123, 255, 0.1);
            }

            .style-badge.secondary {
                border-color: #6c757d;
                background-color: rgba(108, 117, 125, 0.1);
            }

            .style-letter {
                display: block;
                font-size: 2em;
                font-weight: bold;
                color: #333;
            }

            .style-label {
                display: block;
                font-size: 0.9em;
                color: #666;
                margin-top: 5px;
            }

            .disc-content-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
                margin: 20px 0;
            }

            .chart-container {
                display: flex;
                justify-content: center;
                margin: 20px 0;
            }

            .disc-legend {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
                margin-top: 15px;
            }

            .legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .legend-color {
                width: 16px;
                height: 16px;
                border-radius: 50%;
            }

            .d-color { background-color: rgba(220, 53, 69, 0.8); }
            .i-color { background-color: rgba(255, 193, 7, 0.8); }
            .s-color { background-color: rgba(40, 167, 69, 0.8); }
            .c-color { background-color: rgba(0, 123, 255, 0.8); }

            .analysis-content {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }

            .analysis-section {
                margin-bottom: 1.5rem;
                padding: 1rem;
                background: white;
                border-radius: 6px;
                border-left: 3px solid #6f42c1;
            }

            .analysis-section h4 {
                color: #6f42c1;
                margin-top: 0;
                margin-bottom: 0.75rem;
                font-size: 1.1rem;
            }

            .analysis-section p {
                margin: 0;
                line-height: 1.6;
                color: #495057;
            }

            .analysis-content p {
                margin-bottom: 15px;
                line-height: 1.6;
            }

            .dimensions-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
                margin-top: 20px;
            }

            .dimension-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                background-color: #fff;
            }

            .dimension-letter {
                font-size: 1.5em;
                font-weight: bold;
                color: #007bff;
            }

            .dimension-card h4 {
                margin-bottom: 10px;
                color: #333;
            }

            .dimension-card p {
                color: #666;
                font-style: italic;
                margin-bottom: 15px;
            }

            .dimension-card ul {
                list-style: none;
                padding: 0;
            }

            .dimension-card li {
                padding: 5px 0;
                padding-left: 20px;
                position: relative;
            }

            .dimension-card li::before {
                content: "•";
                color: #007bff;
                font-weight: bold;
                position: absolute;
                left: 0;
            }

            .d-dimension { border-left: 4px solid rgba(220, 53, 69, 0.8); }
            .i-dimension { border-left: 4px solid rgba(255, 193, 7, 0.8); }
            .s-dimension { border-left: 4px solid rgba(40, 167, 69, 0.8); }
            .c-dimension { border-left: 4px solid rgba(0, 123, 255, 0.8); }

            .btn-print {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-right: 10px;
            }

            .btn-print:hover {
                background-color: #5a6268;
            }

            .btn-close-disc {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
            }

            .btn-close-disc:hover {
                background-color: #0056b3;
            }

            @media (max-width: 768px) {
                .disc-content-container {
                    grid-template-columns: 1fr;
                }

                .dimensions-grid {
                    grid-template-columns: 1fr;
                }

                .primary-styles {
                    flex-direction: column;
                    align-items: center;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize styles when script loads
    injectDiscModalStyles();

    // Expose the function globally
    global.showDiscProfile = showDiscProfile;
    global.closeDiscModal = closeDiscModal;

})(window);
